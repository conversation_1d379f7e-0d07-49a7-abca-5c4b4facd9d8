html, body,*{
  margin: 0;
  padding: 0;
}
a{
  text-decoration: none;
  color: #333;
}
.w100{
  width: 100%;
}
.backwack{
  -webkit-filter: grayscale(100%); 
  -moz-filter: grayscale(100%); 
  -ms-filter: grayscale(100%); 
  -o-filter: grayscale(100%); 
  filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);  
  _filter:none; 
}
.max-box{
  height: 200px;
  width: 400px;
  overflow-y: hidden;
  position: relative;
}
.max-bgimg-0{
  background: url('../img/popupBgimg/bgimg-0.jpg') no-repeat;
  background-size: 100% 100% ;
}
.xiaoyazi{
  height: 40px;
  width: 40px;
  background: url('../img/xiaoyazi.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  bottom: 5px;
  left: calc(0% - 20px);
  transition: all 0.3s;
  animation: duck 0.5s linear infinite alternate;
  cursor: pointer;
}
@keyframes duck{
  0% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(10deg);
  }
}
.xiaoyazi:hover{
  height: 50px;
  width: 50px;
  transition: all 0.3s;
}
.roasted-duck{
  height: 50px;
  width: 50px;
  background: url('../img/kaoya.png') no-repeat;
  background-size: 100% 100%;
  bottom: 10px;
}
/* 进度条 */
.seep-box{
  background-color: rgba(255, 255, 255, 0.5);
  height: 6px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}
.seep-box>.seep-item{
  background-image: linear-gradient(to right,#00f2fe, #4facfe);
  height: 100%;
  width: 0%;
  transition: all 0.3s;
}

/* 按钮组 */
.btn-list{
  display: flex;
  justify-content: flex-end;
}
.btn-list>.item{
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-list>.item>img{
  width: 28px;
  cursor: pointer;
  transition: all 0.2s;
}
.btn-list>.item>img:hover{
  width: 36px;
  transition: all 0.2s;
}
/* 喝水描述 */
.msg-box{
  position: absolute;
  color: #fff;
  font-size: 14px;
  left: 30px;
  top: 40%;
  text-shadow: 2px 2px 5px #000;
  display: none;
}

/* 设置弹层 */
.set-modal{
  background-color: rgba(255, 255, 255, 0.7);
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  display: none;
}
.mod-title{
  display: flex;
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  background-color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}
.mod-title>.left{
  flex-grow: 1;
  font-size: 16px;
}
.mod-title>.right{
  width: 40px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
}
.mod-title>.right>img{
  height: 24px;
}
.form-list{
  padding: 0 20px;
}
.form-list>.item{
  display: flex;
  height: 30px;
  align-items: center;
}
.form-list>.item>.left{
  display: flex;
  width: 80px;
}
.form-list>.item>.right{
  display: flex;
  flex-grow: 1;
}
.form-btn-name{
  display: flex;
}
.form-btn-name>.item{
  display: flex;
  width: 50%;
  height: 30px;
  align-items: center;
}
.form-btn-name>.item>.left{
  display: flex;
  width: 70px;
  justify-content: center;
}
.form-btn-name>.item>.right{
  display: flex;
  flex-grow: 1;
}
.modal-foort{
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
}
.suc-btn{
  padding: 6px 12px;
  background-color: #1daeff;
  border-radius: 8px;
  color: #fff;
  cursor: pointer;
  margin-left: 10px;
}
.close-btn{
  padding: 6px 12px;
  background-color: #eee;
  border-radius: 8px;
  color: #333;
  cursor: pointer;
  margin-left: 10px;
}

/* 换肤弹框 */
.skin-modal{
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  display: none;
  background-color: rgba(255, 255, 255, 0.7);
  
}
.title-box{
  display: flex;
  height: 30px;
  align-items: center;
  padding: 0 10px;
}
.title-box>div{
  display: flex;
  
  margin-right: 10px;
}
.btn-list-box{
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  padding-top: 0;
}
.btn-box{
  padding: 4px 8px;
  font-size: 14px;
  color: #666;
  text-align: center;
  border-radius: 6px;
  border: solid 1px #ccc;
  margin: 2px 4px;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
}
.btn-box:hover{
  background-color: #fff;
  color: #333;
}
.bgm-loading {
  animation: loadingbgm 3s linear infinite;
}
/* bgm图标旋转 */
@keyframes loadingbgm {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}