let timer = null
let duration = 1800
let seepDuration = 0
let seepNum = 0
let nickTextObj = {}
let timeStopStart = false

// Service Worker 中需要使用 chrome.storage 替代 localStorage
async function setLoacl({key, val, isObj = true}) {
  const data = isObj ? val : {value: val}
  await chrome.storage.local.set({[key]: data})
}

async function getLoacl({key, isObj = true}) {
  const result = await chrome.storage.local.get(key)
  if (!result[key]) return null
  return isObj ? result[key] : result[key].value
}

initSetUp()

function starTimer(fun=function(){}){
  clearInterval(timer)
  timer = setInterval(function(){
    if(seepNum>=100){
      clearInterval(timer)
      noticeWater()
    }else{
      seepDuration +=1
      seepNum =  (seepDuration / duration) * 100
      iconTimer(seepDuration)
      fun({seepNum, seepDuration})
    }
  },1000)
  
  return {seepNum, seepDuration}
}
starTimer()

// 获取基本信息
function getBginfo(){
  return {
    duration,
    seepDuration,
    seepNum
  }
}

// 重置信息
function resetInfo(fun=function(){}){
  clearInterval(timer)
  seepDuration = 0
  seepNum = 0
  starTimer()
  fun({
    duration,
    seepDuration,
    seepNum
  })
}

// 发送通知
async function noticeWater(){
  const {title, content, btnleft, btnright} = await initSetUp()
  chrome.notifications.create({
      type: 'basic',
      iconUrl: '../img/icon.png',
      title,
      buttons: [{
          title: btnleft,
          iconUrl: '../img/daizhu.png'
      }, {
          title: btnright,
          iconUrl: '../img/kuqi.png'
      }],
      message: content
  })
}
chrome.notifications.onButtonClicked.addListener(function (id, btnIndex) {
  if(btnIndex===0){
    resetInfo()
  }
});

// 设置小鸭子下标 - 使用 action API
function iconTimer(num=0){
  const text = num < duration ? Math.floor(num / 60) + '' : '危'
  const color = num < duration ? '#1daeff' : '#ff4000'
  
  chrome.action.setBadgeText({
      text: text
  })
  chrome.action.setBadgeBackgroundColor({
    color: color
  })
}

// 个性化设置存入缓存
async function setUpLocal(data){
  await setLoacl({key:'setUp', val:data})
  await initSetUp()
  // Service Worker 中不能使用 location.reload()
}

// 初始化个性设置
async function initSetUp(){
  const setupData = await getLoacl({key:'setUp'}) || {}
  const {
    duration:durationloc='1800', 
    title='快喝水~', 
    content='已经很久没喝水了，变烤鸭啦~', 
    btnleft='马上喝', 
    btnright='我偏不'
  } = setupData
  duration = Number(durationloc)
  nickTextObj.title = title
  nickTextObj.content = content
  nickTextObj.btnleft = btnleft
  nickTextObj.btnright = btnright
  return nickTextObj
}

// 暂停时间(切换)
function changeTime(flag){
  timeStopStart = flag
  if(timeStopStart){
    clearInterval(timer)
  }else{
    starTimer()
  }
}

// 获取暂停时间状态
function getTimeStart(){
  return timeStopStart
}

// 监听浏览器地址栏变化
chrome.tabs.onUpdated.addListener(async function(tabId,changeInfo,curtab){
  if(changeInfo.status === 'complete'){ 
    // 向content_scripts发送消息
    chrome.tabs.query({ active: true, currentWindow: true }, function (tab) {
      chrome.tabs.sendMessage(tab[0].id, {
        actionType: "tabChange",
        obj:{tabId, changeInfo, curtab}
      });
    });
    const skinType = await getLoacl({key:'skinType', isObj:false}) || 'init'
    const hasJueJin = curtab.url.includes('https://juejin.cn/post')
    if((skinType!=='init' || skinType !== 'dark') && hasJueJin){
      !curtab.url.includes('#') && changeAudio({skin: skinType})
    }
  }
});

// popup切换歌曲回调
async function changeAudio(data){
  const micObj = {
    'video-fengchuimailang':'https://www.qqmc.com/mp3/music239653.mp3',
    'video-huanghun':'https://www.qqmc.com/mp3/music6874743.mp3',
    'video-yuai':'https://www.qqmc.com/mp3/music38615712.mp3',
    'video-gwysgdsj':'https://www.qqmc.com/mp3/music440614.mp3',
    'video-nnhdws':'https://www.qqmc.com/mp3/music221804.mp3',
    'video-dafengchui':'https://www.qqmc.com/mp3/music169428024.mp3',
  }
  const aUrl = micObj[data.skin]
  await setLoacl({key:'skinType', val:data.skin, isObj:false})
  
  // 通知 content script 处理音频
  chrome.tabs.query({ active: true, currentWindow: true }, function (tab) {
    chrome.tabs.sendMessage(tab[0].id, {
      actionType: "updateAudio",
      audioUrl: aUrl,
      skin: data.skin
    });
  });
}

// 获取背景缓存
async function getBgLoacl({key='', isObj=true}){
  return await getLoacl({key, isObj})
}

// 存储静音模式
async function setPlayModel(chexVal){
  const skinType = await getLoacl({key:'skinType', isObj:false}) || 'init'
  await setLoacl({key:'hasAudioMute', val:{chexVal}})
  changeAudio({skin: skinType})
}

// 获取静音模式缓存值
async function getHasAudioMute(){
  const hasAudioMute = await getLoacl({key:'hasAudioMute'}) || {chexVal:false}
  return hasAudioMute.chexVal
}

// 移除掘金标签，暂停歌曲
chrome.tabs.onRemoved.addListener(function(){
  chrome.tabs.getAllInWindow(null,function(tabs){
    const hasJueJin = tabs.some(function(item){
      return item.url.includes('juejin')
    })
    if(!hasJueJin) {
      chrome.tabs.query({}, function(tabs) {
        tabs.forEach(tab => {
          chrome.tabs.sendMessage(tab.id, {
            actionType: "pauseAudio"
          }).catch(() => {}); // 忽略错误
        });
      });
    }
  })
})
