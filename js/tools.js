function setLoacl({key='', val='', isObj=true}){
  const newVal = isObj ? JSON.stringify(val) : val
  localStorage.setItem(key, newVal)
}
function getLoacl({key='', isObj=true}){
  const val = localStorage.getItem(key)
  const newVal = isObj ? JSON.parse(val) : val
  return newVal
}
function delLoacl(key=''){
  localStorage.removeItem(key)
}
// 日期格式化
function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}
// 防抖
function fangdou(fun, time=500){
  return (...arge)=>{
    clearTimeout(fun.id)
    fun.id = setTimeout(()=>{
      fun.apply(this, arge)
    }, time)
  }
}