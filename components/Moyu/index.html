<div id="modalSetId-2" class="set-modal moyu-modal">
  <div class="mod-title">
    <div class="left">
      <span id="modalTitle"></span>
    </div>
    <div id="closeModalId-2" class="right">
      <img src="./img/close.png" />
    </div>
  </div>
  <div  class="moyu-content-box">
    <p id="contentId" class="moyu-text back"></p>
    <div id="moyuLinkListId" class="back anli-list"></div>
    <div class="back toutiao-box">
      <div id="resouTabId" class="title">
        <div class="item title-act" data-type="weibo">微博热搜</div>
        <div class="item" data-type="zhihu">知乎热搜</div>
        <div class="item" data-type="baidu">百度热搜</div>
        <div class="item" data-type="toutiao">今日头条</div>
      </div>
      <div id="resouListId" class="toutiao-list-text"></div>
    </div>
  </div>
</div>

<style>
  .back{
    background-color: rgba(255, 255, 255, 0.6);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 4px;
  }
  .moyu-modal{
    background: #fff url('../../img/moyubg.jpg') no-repeat;
    background-size: 100% 100%;
  }
  .moyu-modal .mod-title{
    border-bottom: solid 1px #ebebeb;
    background-color: rgba(255, 255, 255, 0.5);
  }
  .moyu-content-box{
    overflow-y: auto;
    height: calc(100vh - 60px);
    padding: 10px;
  }
  .moyu-text{
    font-size: 16px;
    color: #333;
  }
  .anli-list{
    display: flex;
    flex-wrap: wrap;
  }
  .anli-list>.item{
    background-color: #1daeff;
    color: #fff;
    opacity: 0.8;
    padding: 4px 12px;
    border-radius: 4px;
    margin: 4px;
  }
  .anli-list>.item:hover{
    opacity: 1;
  }
  .toutiao-box>.title{
    border-bottom: solid 1px #ccc;
    margin-bottom: 10px;
    display: flex;
  }
  .toutiao-box>.title>div{
    height: 100%;
    width: 100px;
    text-align: center;
    cursor: pointer;
    font-size: 16px;
    padding-bottom: 8px;
  }
  .title-act{
    color: #1daeff;
    border-bottom: solid 2px #1daeff;
    margin-bottom: -2px;
  }
  .toutiao-box>.title>div:hover{
    color: #1daeff;
    border-bottom: solid 2px #1daeff;
    margin-bottom: -2px;
  }
  .toutiao-box>.title>div:last-child{
    border-right: none;
  }
  .toutiao-list-text{
    min-height: 300px;
  }
  .toutiao-list-text a{
    color: #535151;
    line-height: 28px;
    font-size: 14px;
  }
  .toutiao-list-text a:hover{
    color: #1daeff;
  }
</style>